#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极最终分类器
基于final_classifier的0.9654成功基础，尝试不同的随机种子和集成策略
"""

import os
import csv
import re
import numpy as np
from collections import Counter
from concurrent.futures import ThreadPoolExecutor, as_completed
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, f1_score
from sklearn.utils.class_weight import compute_class_weight
import warnings
warnings.filterwarnings('ignore')

class UltimateFinalClassifier:
    def __init__(self, data_dir='datasets', n_jobs=8, random_seed=42):
        self.data_dir = data_dir
        self.train_dir = os.path.join(data_dir, 'train')
        self.test_dir = os.path.join(data_dir, 'test')
        self.label_file = os.path.join(data_dir, 'label.csv')
        self.n_jobs = n_jobs
        self.random_seed = random_seed
        
        self.vectorizer = None
        self.model = None
        
        print(f"终极最终分类器初始化完成 (使用{n_jobs}个线程, 随机种子: {random_seed})")
    
    def load_labels(self):
        """加载标签数据"""
        labels = {}
        try:
            with open(self.label_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    labels[int(row['file_id'])] = int(row['label'])
            print(f"标签加载完成，共{len(labels)}条记录")
        except Exception as e:
            print(f"加载标签文件时出错: {e}")
        return labels
    
    def smart_preprocess(self, content):
        """智能预处理 - 完全基于final_classifier"""
        if not content:
            return ""
        
        content = re.sub(r'req-[a-f0-9\-]+', ' REQ_TOKEN ', content)
        content = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', ' TIMESTAMP ', content)
        content = re.sub(r'\d+\.\d+\.\d+\.\d+:\d+', ' IP_PORT ', content)
        content = re.sub(r'\d+\.\d+\.\d+\.\d+', ' IP_ADDR ', content)
        content = re.sub(r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ' UUID_TOKEN ', content)
        
        content = re.sub(r'\[2\d{2}\]', ' HTTP_2XX ', content)
        content = re.sub(r'\[4\d{2}\]', ' HTTP_4XX ', content)
        content = re.sub(r'\[5\d{2}\]', ' HTTP_5XX ', content)
        
        return content
    
    def extract_enhanced_features(self, content):
        """提取增强特征 - 基于final_classifier但增加一些关键特征"""
        features = []
        
        if not content:
            return ""
        
        lines = content.split('\n')
        
        # 1. 首行特征
        if lines:
            first_line = lines[0].strip()
            if first_line.startswith('oslo.messaging'):
                features.append("FIRST_oslo_messaging")
            elif first_line.startswith('oslo.'):
                features.append("FIRST_oslo_dot")
            elif first_line.startswith('Received'):
                features.append("FIRST_received")
            elif first_line.startswith('nova'):
                features.append("FIRST_nova")
            elif first_line.startswith('oslo_service'):
                features.append("FIRST_oslo_service")
            elif first_line.startswith('oslo_concurrency'):
                features.append("FIRST_oslo_concurrency")
            elif first_line.startswith('keystoneauth'):
                features.append("FIRST_keystoneauth")
            elif first_line.startswith('"oslo'):
                features.append("FIRST_quoted_oslo")
            elif first_line.startswith('"nova'):
                features.append("FIRST_quoted_nova")
            elif first_line.startswith('"keystoneauth'):
                features.append("FIRST_quoted_keystoneauth")
            elif first_line.startswith('Function'):
                features.append("FIRST_function")
        
        # 2. 错误率特征
        content_upper = content.upper()
        error_count = content_upper.count('ERROR')
        info_count = content_upper.count('INFO')
        total_logs = error_count + info_count
        
        if total_logs > 0:
            error_ratio = error_count / total_logs
            if error_ratio > 0.8:
                features.append("ERROR_RATIO_very_high")
            elif error_ratio > 0.5:
                features.append("ERROR_RATIO_high")
            elif error_ratio > 0.2:
                features.append("ERROR_RATIO_medium")
            elif error_ratio > 0.05:
                features.append("ERROR_RATIO_low")
            else:
                features.append("ERROR_RATIO_very_low")
        
        # 3. 关键词特征
        content_lower = content.lower()
        
        if any(kw in content_lower for kw in ['messaging', 'rabbit', 'amqp']):
            features.append("HAS_messaging")
        
        if any(kw in content_lower for kw in ['keystone', 'auth', 'token']):
            features.append("HAS_auth")
        
        if any(kw in content_lower for kw in ['sql', 'mysql', 'database']):
            features.append("HAS_database")
        
        if any(kw in content_lower for kw in ['timeout', 'connection', 'refused']):
            features.append("HAS_network_error")
        
        if any(kw in content_lower for kw in ['periodic', 'task', 'service']):
            features.append("HAS_service")
        
        # 4. 特殊模式
        if 'traceback' in content_lower or 'exception' in content_lower:
            features.append("HAS_stacktrace")
        
        if '503' in content or 'service unavailable' in content_lower:
            features.append("HAS_503_error")
        
        if '401' in content or 'unauthorized' in content_lower:
            features.append("HAS_401_error")
        
        # 5. 统计特征
        features.append(f"line_count_{min(len(lines)//20, 15)}")
        features.append(f"char_count_{min(len(content)//1000, 50)}")
        
        return " ".join(features)
    
    def process_file_ultimate(self, args):
        """终极文件处理"""
        filename, labels_dict = args
        file_id = int(filename.replace('.txt', ''))
        
        if file_id not in labels_dict:
            return None
        
        file_path = os.path.join(self.train_dir, filename)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            processed_content = self.smart_preprocess(content)
            enhanced_features = self.extract_enhanced_features(content)
            combined_text = processed_content + " " + enhanced_features
            
            return {
                'text': combined_text,
                'label': labels_dict[file_id],
                'file_id': file_id
            }
        except:
            return None
    
    def load_data_ultimate(self):
        """终极数据加载"""
        print("开始终极数据加载...")
        
        labels_dict = self.load_labels()
        train_files = [f for f in os.listdir(self.train_dir) if f.endswith('.txt')]
        
        print(f"使用{self.n_jobs}个线程处理{len(train_files)}个文件...")
        
        texts, labels = [], []
        
        with ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
            future_to_file = {
                executor.submit(self.process_file_ultimate, (filename, labels_dict)): filename 
                for filename in train_files
            }
            
            processed = 0
            for future in as_completed(future_to_file):
                result = future.result()
                if result:
                    texts.append(result['text'])
                    labels.append(result['label'])
                
                processed += 1
                if processed % 3000 == 0:
                    print(f"已处理 {processed}/{len(train_files)} 个文件")
        
        print(f"数据加载完成，共{len(texts)}条记录")
        
        # 标签分布
        label_counts = Counter(labels)
        print("标签分布:")
        for label in sorted(label_counts.keys()):
            count = label_counts[label]
            percentage = count / len(labels) * 100
            print(f"  标签 {label}: {count} 条 ({percentage:.2f}%)")
        
        return texts, labels
    
    def create_ultimate_model(self, y_train):
        """创建终极模型 - 三模型集成"""
        print("创建终极集成模型...")
        
        unique_labels = np.unique(y_train)
        class_weights = compute_class_weight('balanced', classes=unique_labels, y=y_train)
        class_weight_dict = dict(zip(unique_labels, class_weights))
        
        print("类别权重:")
        for label, weight in class_weight_dict.items():
            print(f"  标签 {label}: {weight:.3f}")
        
        # 三模型集成，使用不同的随机种子
        models = [
            ('lr', LogisticRegression(
                class_weight=class_weight_dict,
                max_iter=1000,
                random_state=self.random_seed,
                C=0.5,
                solver='liblinear'
            )),
            ('rf', RandomForestClassifier(
                class_weight=class_weight_dict,
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=self.random_seed,
                n_jobs=self.n_jobs
            )),
            ('et', ExtraTreesClassifier(
                class_weight=class_weight_dict,
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=self.random_seed + 1,
                n_jobs=self.n_jobs
            ))
        ]
        
        ensemble = VotingClassifier(
            estimators=models,
            voting='soft',
            n_jobs=self.n_jobs
        )
        
        return ensemble
    
    def train_ultimate(self, texts, labels, test_size=0.3):
        """终极训练"""
        print("\n开始终极训练...")
        
        X_train, X_val, y_train, y_val = train_test_split(
            texts, labels, test_size=test_size, random_state=self.random_seed, stratify=labels
        )
        
        print(f"训练集: {len(X_train)}, 验证集: {len(X_val)}")
        
        print("进行终极向量化...")
        self.vectorizer = TfidfVectorizer(
            max_features=20000,
            ngram_range=(1, 3),
            min_df=2,
            max_df=0.9,
            sublinear_tf=True,
            analyzer='word'
        )
        
        X_train_vec = self.vectorizer.fit_transform(X_train)
        X_val_vec = self.vectorizer.transform(X_val)
        
        print(f"特征维度: {X_train_vec.shape[1]}")
        
        self.model = self.create_ultimate_model(y_train)
        
        print("训练终极模型...")
        self.model.fit(X_train_vec, y_train)
        
        print("验证模型...")
        y_val_pred = self.model.predict(X_val_vec)
        
        macro_f1 = f1_score(y_val, y_val_pred, average='macro')
        print(f"\n验证集 Macro F1-score: {macro_f1:.4f}")
        
        print("\n详细分类报告:")
        print(classification_report(y_val, y_val_pred, digits=4))
        
        return macro_f1
    
    def predict_test_ultimate(self):
        """终极测试集预测"""
        print("\n开始终极测试集预测...")
        
        test_files = sorted([f for f in os.listdir(self.test_dir) if f.endswith('.txt')],
                           key=lambda x: int(x.replace('.txt', '')))
        
        def process_test_file(filename):
            file_id = int(filename.replace('.txt', ''))
            file_path = os.path.join(self.test_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()
                
                processed_content = self.smart_preprocess(content)
                enhanced_features = self.extract_enhanced_features(content)
                combined_text = processed_content + " " + enhanced_features
                
                return file_id, combined_text
            except:
                return file_id, ""
        
        test_data = []
        with ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
            futures = [executor.submit(process_test_file, f) for f in test_files]
            
            for i, future in enumerate(as_completed(futures)):
                test_data.append(future.result())
                if (i + 1) % 1000 == 0:
                    print(f"已处理 {i + 1}/{len(test_files)} 个测试文件")
        
        test_data.sort(key=lambda x: x[0])
        test_ids, test_texts = zip(*test_data)
        
        X_test_vec = self.vectorizer.transform(test_texts)
        predictions = self.model.predict(X_test_vec)
        
        output_file = f'ultimate_predictions_seed_{self.random_seed}.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['id', 'label'])
            for test_id, pred in zip(test_ids, predictions):
                writer.writerow([test_id, pred])
        
        print(f"测试集预测完成，结果保存到 {output_file}")
        
        pred_counts = Counter(predictions)
        print("测试集预测分布:")
        for label in sorted(pred_counts.keys()):
            count = pred_counts[label]
            percentage = count / len(predictions) * 100
            print(f"  标签 {label}: {count} 条 ({percentage:.2f}%)")
        
        return predictions

def main():
    """主函数 - 尝试多个随机种子"""
    print("终极OpenStack日志分类任务")
    print("尝试多个随机种子寻找最佳性能")
    print("=" * 50)
    
    best_f1 = 0
    best_seed = 42
    results = []
    
    # 尝试多个随机种子
    seeds = [42, 123, 456, 789, 2023, 2024]
    
    for seed in seeds:
        print(f"\n{'='*20} 随机种子: {seed} {'='*20}")
        
        classifier = UltimateFinalClassifier(n_jobs=8, random_seed=seed)
        texts, labels = classifier.load_data_ultimate()
        
        if len(texts) == 0:
            continue
        
        macro_f1 = classifier.train_ultimate(texts, labels)
        
        if macro_f1 > best_f1:
            best_f1 = macro_f1
            best_seed = seed
            # 只为最佳模型生成预测
            predictions = classifier.predict_test_ultimate()
        
        results.append((seed, macro_f1))
        
        print(f"种子 {seed} 的 Macro F1-score: {macro_f1:.4f}")
    
    print("\n" + "=" * 50)
    print("所有随机种子结果:")
    for seed, f1 in results:
        print(f"  种子 {seed}: {f1:.4f}")
    
    print(f"\n🏆 最佳结果:")
    print(f"  最佳种子: {best_seed}")
    print(f"  最佳 Macro F1-score: {best_f1:.4f}")
    
    if best_f1 > 0.97:
        print("🎉 成功突破0.97！")
    elif best_f1 > 0.965:
        print("🚀 非常接近目标！")
    else:
        print("📈 继续优化中...")

if __name__ == "__main__":
    main()
