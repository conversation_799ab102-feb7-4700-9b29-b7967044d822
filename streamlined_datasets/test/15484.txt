"nova.compute.manager [REQ_ID - - - - -] [instance: UUI<PERSON>] Synchronizing instance power state after lifecycle event "Resumed"; current vm_state: building, current task_state: deleting, current DB power_state: 0, VM power_state: 1 handle_lifecycle_event /usr/lib/python2.7/site-packages/nova/compute/manager.py:1276"
nova.compute.utils [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Conflict updating instance UUID. Expected: {'task_state': [u'spawning']}. Actual: {'task_state': u'deleting'} notify_about_instance_usage /usr/lib/python2.7/site-packages/nova/compute/utils.py:284
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] deallocate_for_instance() deallocate_for_instance /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:801
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}a1d1bd121dd086d62856fc3509c7da52b2312d39" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] During sync_power_state the instance has a pending task (deleting). Skip.
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 853 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 19:35:53 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Instance cache missing network info. _get_preexisting_port_ids /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:1659
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X DELETE http://controller:9696/v2.0/ports/UUID.json -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}a1d1bd121dd086d62856fc3509c7da52b2312d39" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"nova.compute.manager [REQ_ID - - - - -] [instance: UUID] Synchronizing instance power state after lifecycle event "Resumed"; current vm_state: building, current task_state: deleting, current DB power_state: 0, VM power_state: 1 handle_lifecycle_event /usr/lib/python2.7/site-packages/nova/compute/manager.py:1276"
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] During sync_power_state the instance has a pending task (deleting). Skip.
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [204] Content-Length: 0 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 19:35:53 GMT Connection: keep-alive _http_log_response /usr/lib/python2.7/site-packages/keystoneauth1/session.py:277"
nova.network.base_api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Updating instance_info_cache with network_info: [] update_instance_cache_with_nw_info /usr/lib/python2.7/site-packages/nova/network/base_api.py:43
"nova.virt.libvirt.vif [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] vif_type=bridge instance=Instance(access_ip_v4=None,access_ip_v6=None,architecture=None,auto_disk_config=True,availability_zone='nova',cell_name=None,cleaned=False,config_drive='',created_at=2022-01-18T19:34:05Z,default_ephemeral_device=None,default_swap_device=None,deleted=False,deleted_at=None,disable_terminate=False,display_description='d',display_name='d',ec2_ids=<?>,ephemeral_gb=0,ephemeral_key_uuid=None,fault=<?>,flavor=Flavor(5),host='h432-l13',hostname='d',id=119,image_ref='UUID',info_cache=InstanceInfoCache,instance_type_id=5,kernel_id='',key_data=None,key_name=None,launch_index=0,launched_at=None,launched_on='h432-l13',locked=False,locked_by=None,memory_mb=2048,metadata={},migration_context=<?>,new_flavor=None,node='h432-l13',numa_topology=None,old_flavor=None,os_type=None,pci_devices=PciDeviceList,pci_requests=<?>,power_state=0,progress=0,project_id='7fe5d95324c448b695017fc3a63492b4',ramdisk_id='',reservation_id='r-n9e2y6lg',root_device_name='/dev/vda',root_gb=20,security_groups=SecurityGroupList,services=<?>,shutdown_terminate=False,system_metadata={image_base_image_ref='UUID',image_container_format='bare',image_disk_format='qcow2',image_min_disk='20',image_min_ram='0'},tags=<?>,task_state='deleting',terminated_at=None,updated_at=2022-01-18T19:35:47Z,user_data=None,user_id='43b996f08a374a359872ac4444258295',uuid=UUID,vcpu_model=<?>,vcpus=1,vm_mode=None,vm_state='building') vif=VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': u'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {u'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': u'gateway', 'address': u'IP_ADDR'})})], 'meta': {u'injected': False, u'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', u'should_create_bridge': True, u'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tapd0937f31-b1', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:9f:53:0a', 'active': False, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None}) unplug /usr/lib/python2.7/site-packages/nova/virt/libvirt/vif.py:961"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] deallocate_for_instance() deallocate_for_instance /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:801
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}a1d1bd121dd086d62856fc3509c7da52b2312d39" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"