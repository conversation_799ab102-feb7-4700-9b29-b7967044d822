oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rebooting_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._heal_instance_info_cache run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
nova.network.neutronv2.api [REQ_ID - - - - -] [instance: UUID] _get_instance_nw_info() _get_instance_nw_info /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:910
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?tenant_id=7fe5d95324c448b695017fc3a63492b4&device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}f003102cfcbd88a2e1638dc02b65eaf5bc5e7b80" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [503] Content-Length: 100 Content-Type: text/plain; charset=UTF-8 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 12:28:39 GMT Connection: keep-alive"
neutronclient.v2_0.client [REQ_ID - - - - -] Error message: 503 Service Unavailable
"nova.compute.manager [instance: UUID]     self._handle_fault_response(status_code, replybody, resp)"
"nova.compute.manager [instance: UUID]     exception_handler_v20(status_code, error_body)"
"nova.compute.manager [instance: UUID]   File "/usr/lib/python2.7/site-packages/neutronclient/v2_0/client.py", line 84, in exception_handler_v20"
nova.compute.manager [instance: UUID] ServiceUnavailable: 503 Service Unavailable
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] An error occurred while refreshing the network cache.
nova.compute.manager [instance: UUID] Traceback (most recent call last):
"nova.compute.manager [instance: UUID]     self.network_api.get_instance_nw_info(context, instance)"
"nova.compute.manager [instance: UUID]   File "/usr/lib/python2.7/site-packages/nova/network/base_api.py", line 253, in get_instance_nw_info"
"nova.compute.manager [instance: UUID]   File "/usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py", line 917, in _get_instance_nw_info"