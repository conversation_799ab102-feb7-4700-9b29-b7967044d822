oslo.messaging._drivers.impl_rabbit     self._connection = self._establish_connection()
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 696, in _establish_connection"
AMQP server on controller:5672 is unreachable: [Errno 111] ECONNREFUSED. Trying again in 8 seconds.
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/transport/pyamqp.py", line 116, in establish_connection"
"oslo.messaging._drivers.impl_rabbit     return fun(*args, **kwargs)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 237, in connect"
oslo.messaging._drivers.impl_rabbit     return self.connection
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 741, in connection"
oslo.messaging._drivers.impl_rabbit     self._connection = self._establish_connection()
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 696, in _establish_connection"
oslo.messaging._drivers.impl_rabbit     conn = self.transport.establish_connection()
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/utils/__init__.py", line 246, in retry_over_time"
oslo.messaging._drivers.impl_rabbit     conn = self.Connection(**opts)
"oslo.messaging._drivers.impl_rabbit     return TCPTransport(host, connect_timeout)"
"oslo.messaging._drivers.impl_rabbit     self.transport = self.Transport(host, connect_timeout, ssl)"