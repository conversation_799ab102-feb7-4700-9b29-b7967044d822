"nova.compute.claims [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Total disk: 390 GB, used: 62.00 GB"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/extensions.json -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6af78e8f227ee6f840649ebe547719bad28fbc5a" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 5792 X-Openstack-Request-Id: REQ_ID Date: Fri, 21 Jan 2022 14:16:09 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] allocate_for_instance() allocate_for_instance /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:557
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/networks.json?id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6af78e8f227ee6f840649ebe547719bad28fbc5a" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Fri, 21 Jan 2022 14:16:09 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/security-groups.json?tenant_id=7fe5d95324c448b695017fc3a63492b4 -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6af78e8f227ee6f840649ebe547719bad28fbc5a" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 2277 X-Openstack-Request-Id: REQ_ID Date: Fri, 21 Jan 2022 14:16:09 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X POST http://controller:9696/v2.0/ports.json -H "User-Agent: python-neutronclient" -H "Content-Type: application/json" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}ce667f076d375fcef4629b25cddd3e3bc2cf13fc" -d '{"port": {"binding:host_id": "h432-l13", "admin_state_up": true, "network_id": "UUID", "dns_name": "xxx", "device_owner": "compute:nova", "tenant_id": "7fe5d95324c448b695017fc3a63492b4", "security_groups": ["UUID"], "device_id": "UUID"}}' _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [201] Content-Type: application/json; charset=UTF-8 Content-Length: 848 X-Openstack-Request-Id: REQ_ID Date: Fri, 21 Jan 2022 14:16:09 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Successfully created port: UUID _create_port /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:261
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] _get_instance_nw_info() _get_instance_nw_info /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:910
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?tenant_id=7fe5d95324c448b695017fc3a63492b4&device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}ce667f076d375fcef4629b25cddd3e3bc2cf13fc" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 851 X-Openstack-Request-Id: REQ_ID Date: Fri, 21 Jan 2022 14:16:09 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Instance cache missing network info. _get_preexisting_port_ids /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:1659