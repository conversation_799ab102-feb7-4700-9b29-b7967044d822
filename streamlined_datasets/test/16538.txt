"nova.network.base_api [REQ_ID - - - - -] [instance: UUID] Updating instance_info_cache with network_info: [VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': 'fixed', 'floating_ips': [IP({'meta': {}, 'version': 4, 'type': 'floating', 'address': u'IP_ADDR'})], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': 'gateway', 'address': u'IP_ADDR'})})], 'meta': {'injected': False, 'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', 'should_create_bridge': True, 'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tapdfba387e-1e', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:00:7d:2a', 'active': True, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None})] update_instance_cache_with_nw_info /usr/lib/python2.7/site-packages/nova/network/base_api.py:43"
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rebooting_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._reclaim_queued_deletes run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_unconfirmed_resizes run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_volume_usage run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rescued_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._check_instance_build_time run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager.update_available_resource run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._instance_usage_audit run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._heal_instance_info_cache run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
nova.network.neutronv2.api [REQ_ID - - - - -] [instance: UUID] _get_instance_nw_info() _get_instance_nw_info /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:910
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?tenant_id=7fe5d95324c448b695017fc3a63492b4&device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}74e5c89929bfe7a008bec302a8c763d561235fdc" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Thu, 20 Jan 2022 02:08:21 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/networks.json?id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}74e5c89929bfe7a008bec302a8c763d561235fdc" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"