oslo_concurrency.processutils [REQ_ID - - - - -] Running cmd (subprocess): /usr/bin/python2 -m oslo_concurrency.prlimit --as=1073741824 --cpu=2 -- env LC_ALL=C LANG=C qemu-img info /var/lib/nova/instances/UUID/disk execute /usr/lib/python2.7/site-packages/oslo_concurrency/processutils.py:344
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [401] Content-Type: text/html; charset=UTF-8 Content-Length: 23 Www-Authenticate: Keystone uri='http://controller:5000' X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 15:25:31 GMT Connection: keep-alive"
keystoneauth.identity.v3.base [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Making authentication request to http://controller:35357/v3/auth/tokens get_auth_ref /usr/lib/python2.7/site-packages/keystoneauth1/identity/v3/base.py:165
nova.virt.disk.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Checking if we can resize image /var/lib/nova/instances/UUID/disk. size=21474836480 can_resize_image /usr/lib/python2.7/site-packages/nova/virt/disk/api.py:203
nova.virt.disk.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Checking if we can resize image /var/lib/nova/instances/UUID/disk. size=21474836480 can_resize_image /usr/lib/python2.7/site-packages/nova/virt/disk/api.py:203
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [201] Content-Type: application/json; charset=UTF-8 Content-Length: 848 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 15:25:32 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Successfully created port: UUID _create_port /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:261
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] _get_instance_nw_info() _get_instance_nw_info /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:910
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?tenant_id=7fe5d95324c448b695017fc3a63492b4&device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}0fa607f07c1e8a5c3e211fe5eeaffc3c04f3d9cb" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
nova.virt.disk.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Cannot resize image /var/lib/nova/instances/UUID/disk to a smaller size. can_resize_image /usr/lib/python2.7/site-packages/nova/virt/disk/api.py:209
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 851 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 15:25:32 GMT Connection: keep-alive"
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Instance cache missing network info. _get_preexisting_port_ids /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:1659
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/floatingips.json?fixed_ip_address=IP_ADDR&port_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}0fa607f07c1e8a5c3e211fe5eeaffc3c04f3d9cb" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 19 X-Openstack-Request-Id: REQ_ID Date: Tue, 18 Jan 2022 15:25:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/subnets.json?id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}433e706094cce566c5e5f20690761fea65b14e4c" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"