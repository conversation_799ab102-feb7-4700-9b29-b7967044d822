"oslo.messaging._drivers.impl_rabbit     return TCPTransport(host, connect_timeout)"
"oslo.messaging._drivers.impl_rabbit     return create_transport(host, connect_timeout, ssl)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/connection.py", line 186, in Transport"
"oslo.messaging._drivers.impl_rabbit     self.transport = self.Transport(host, connect_timeout, ssl)"
oslo.messaging._drivers.impl_rabbit     conn = self.Connection(**opts)
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/transport.py", line 299, in create_transport"
oslo.messaging._drivers.impl_rabbit     conn = self.transport.establish_connection()
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/transport/pyamqp.py", line 116, in establish_connection"
oslo.messaging._drivers.impl_rabbit Traceback (most recent call last):
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/utils/__init__.py", line 246, in retry_over_time"
"oslo.messaging._drivers.impl_rabbit     return fun(*args, **kwargs)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 237, in connect"
Received recoverable error from kombu: on_error /usr/lib/python2.7/site-packages/oslo_messaging/_drivers/impl_rabbit.py:674
oslo.messaging._drivers.impl_rabbit     return self.connection
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 741, in connection"