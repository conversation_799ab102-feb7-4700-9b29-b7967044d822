oslo_concurrency.processutils [REQ_ID - - - - -] Running cmd (subprocess): /usr/bin/python2 -m oslo_concurrency.prlimit --as=1073741824 --cpu=2 -- env LC_ALL=C LANG=C qemu-img info /var/lib/nova/instances/UUID/disk execute /usr/lib/python2.7/site-packages/oslo_concurrency/processutils.py:344
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rebooting_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rescued_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._sync_scheduler_instance_info run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._sync_power_states run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
"Lock "UUID" acquired by "nova.compute.manager.query_driver_power_state_and_sync" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
"Lock "UUID" acquired by "nova.compute.manager.query_driver_power_state_and_sync" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
"Lock "UUID" acquired by "nova.compute.manager.query_driver_power_state_and_sync" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
"Lock "UUID" acquired by "nova.compute.manager.query_driver_power_state_and_sync" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
"Lock "UUID" acquired by "nova.compute.manager.query_driver_power_state_and_sync" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
"Lock "UUID" released by "nova.compute.manager.query_driver_power_state_and_sync" :: held 0.059s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"
"Lock "UUID" released by "nova.compute.manager.query_driver_power_state_and_sync" :: held 0.061s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"
"Lock "UUID" released by "nova.compute.manager.query_driver_power_state_and_sync" :: held 0.066s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"
"Lock "UUID" released by "nova.compute.manager.query_driver_power_state_and_sync" :: held 0.064s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"