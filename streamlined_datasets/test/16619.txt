"oslo_concurrency.lockutils [REQ_ID - - - - -] Lock "compute_resources" acquired by "nova.compute.resource_tracker._update_available_resource" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._check_instance_build_time run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_unconfirmed_resizes run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._heal_instance_info_cache run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
nova.network.neutronv2.api [REQ_ID - - - - -] [instance: UUID] _get_instance_nw_info() _get_instance_nw_info /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:910
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?tenant_id=7fe5d95324c448b695017fc3a63492b4&device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6cbb9cd275541dbbc0fe7243bd5e0e7b6bf5c625" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Thu, 20 Jan 2022 04:34:48 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/networks.json?id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6cbb9cd275541dbbc0fe7243bd5e0e7b6bf5c625" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Thu, 20 Jan 2022 04:34:48 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/floatingips.json?fixed_ip_address=IP_ADDR&port_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6cbb9cd275541dbbc0fe7243bd5e0e7b6bf5c625" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 426 X-Openstack-Request-Id: REQ_ID Date: Thu, 20 Jan 2022 04:34:48 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/subnets.json?id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6cbb9cd275541dbbc0fe7243bd5e0e7b6bf5c625" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 543 X-Openstack-Request-Id: REQ_ID Date: Thu, 20 Jan 2022 04:34:48 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?network_id=UUID&device_owner=network%3Adhcp -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}6cbb9cd275541dbbc0fe7243bd5e0e7b6bf5c625" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"