"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/transport.py", line 182, in write_frame"
oslo.messaging._drivers.impl_rabbit error: [Errno 104] Connection reset by peer
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 377, in sendall"
"oslo.messaging._drivers.impl_rabbit     tail = self.send(data, flags)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 359, in send"
"oslo.messaging._drivers.impl_rabbit     total_sent += fd.send(data[total_sent:], flags)"
oslo.messaging._drivers.impl_rabbit
"oslo.messaging._drivers.impl_rabbit     nowait=nowait, passive=passive,"
"oslo.messaging._drivers.impl_rabbit     frame_type, channel, size, payload, 0xce,"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/entity.py", line 174, in declare"
oslo.messaging._drivers.impl_rabbit Traceback (most recent call last):
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/messaging.py", line 105, in declare"
oslo.messaging._drivers.impl_rabbit     self.exchange.declare()
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 436, in _ensured"
"oslo.messaging._drivers.impl_rabbit     return fun(*args, **kwargs)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/kombu/connection.py", line 508, in __call__"
"oslo.messaging._drivers.impl_rabbit     return fun(*args, channel=channels[0], **kwargs), channels[0]"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/oslo_messaging/_drivers/impl_rabbit.py", line 723, in execute_method"
oslo.messaging._drivers.impl_rabbit [REQ_ID - - - - -] Received recoverable error from kombu: on_error /usr/lib/python2.7/site-packages/oslo_messaging/_drivers/impl_rabbit.py:674
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/oslo_messaging/_drivers/impl_rabbit.py", line 1086, in _publish"