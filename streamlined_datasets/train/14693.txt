oslo_service.periodic_task     retry=self.retry)
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/oslo_messaging/rpc/client.py", line 158, in call"
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/oslo_messaging/_drivers/amqpdriver.py", line 461, in _send"
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/nova/conductor/rpcapi.py", line 240, in object_class_action_versions"
"oslo_service.periodic_task     args, kwargs)"
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/oslo_versionedobjects/base.py", line 174, in wrapper"
oslo_service.periodic_task     use_slave=True)
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/nova/compute/manager.py", line 5778, in _heal_instance_info_cache"
"oslo_service.periodic_task     args=args, kwargs=kwargs)"
"oslo_service.periodic_task     task(self, context)"
"oslo_service.periodic_task   File "/usr/lib/python2.7/site-packages/oslo_service/periodic_task.py", line 220, in run_periodic_tasks"
oslo_service.periodic_task Traceback (most recent call last):
oslo_service.periodic_task [REQ_ID - - - - -] Error during ComputeManager._heal_instance_info_cache
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._check_instance_build_time run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._sync_power_states run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215