"nova.network.base_api [REQ_ID - - - - -] [instance: UUID] Updating instance_info_cache with network_info: [VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': 'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': 'gateway', 'address': u'IP_ADDR'})})], 'meta': {'injected': False, 'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', 'should_create_bridge': True, 'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tap20230a5b-c5', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:99:e6:d6', 'active': True, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None})] update_instance_cache_with_nw_info /usr/lib/python2.7/site-packages/nova/network/base_api.py:43"
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] Updated the network info_cache for instance _heal_instance_info_cache /usr/lib/python2.7/site-packages/nova/compute/manager.py:5803
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._check_instance_build_time run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_volume_usage run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._cleanup_running_deleted_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager.update_available_resource run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
"oslo_concurrency.lockutils [REQ_ID - - - - -] Lock "compute_resources" acquired by "nova.compute.resource_tracker._update_available_resource" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
"oslo_concurrency.lockutils [REQ_ID - - - - -] Lock "compute_resources" released by "nova.compute.resource_tracker._update_available_resource" :: held 0.186s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._instance_usage_audit run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rescued_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rebooting_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
nova.servicegroup.drivers.db
"nova.servicegroup.drivers.db [u'Traceback (most recent call last):\n', u'  File "/usr/lib/python2.7/site-packages/nova/conductor/manager.py", line 85, in _object_dispatch\n    return getattr(target, method)(*args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/oslo_versionedobjects/base.py", line 223, in wrapper\n    return fn(self, *args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/nova/objects/service.py", line 297, in save\n    db_service = db.service_update(self._context, self.id, updates)\n', u'  File "/usr/lib/python2.7/site-packages/nova/db/api.py", line 177, in service_update\n    return IMPL.service_update(context, service_id, values)\n', u'  File "/usr/lib/python2.7/site-packages/oslo_db/api.py", line 148, in wrapper\n    ectxt.value = e.inner_exc\n', u'  File "/usr/lib/python2.7/site-packages/oslo_utils/excutils.py", line 220, in __exit__\n    self.force_reraise()\n', u'  File "/usr/lib/python2.7/site-packages/oslo_utils/excutils.py", line 196, in force_reraise\n    six.reraise(self.type_, self.value, self.tb)\n', u'  File "/usr/lib/python2.7/site-packages/oslo_db/api.py", line 138, in wrapper\n    return f(*args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/nova/db/sqlalchemy/api.py", line 300, in wrapped\n    return f(context, *args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/nova/db/sqlalchemy/api.py", line 609, in service_update\n    service_ref = service_get(context, service_id)\n', u'  File "/usr/lib/python2.7/site-packages/nova/db/sqlalchemy/api.py", line 315, in wrapped\n    return f(context, *args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/nova/db/sqlalchemy/api.py", line 503, in service_get\n    result = query.first()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/query.py", line 2634, in first\n    ret = list(self[0:1])\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/query.py", line 2457, in __getitem__\n    return list(res)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/query.py", line 2736, in __iter__\n    return self._execute_and_instances(context)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/query.py", line 2749, in _execute_and_instances\n    close_with_result=True)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/query.py", line 2740, in _connection_from_session\n    **kw)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/session.py", line 905, in connection\n    execution_options=execution_options)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/session.py", line 910, in _connection_for_bind\n    engine, execution_options)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/orm/session.py", line 334, in _connection_for_bind\n    conn = bind.contextual_connect()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/base.py", line 2039, in contextual_connect\n    self._wrap_pool_connect(self.pool.connect, None),\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/base.py", line 2078, in _wrap_pool_connect\n    e, dialect, self)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/base.py", line 1401, in _handle_dbapi_exception_noconnection\n    util.raise_from_cause(newraise, exc_info)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/util/compat.py", line 200, in raise_from_cause\n    reraise(type(exception), exception, tb=exc_tb)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/base.py", line 2074, in _wrap_pool_connect\n    return fn()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 376, in connect\n    return _ConnectionFairy._checkout(self)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 713, in _checkout\n    fairy = _ConnectionRecord.checkout(pool)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 485, in checkout\n    rec.checkin()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/util/langhelpers.py", line 60, in __exit__\n    compat.reraise(exc_type, exc_value, exc_tb)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 482, in checkout\n    dbapi_connection = rec.get_connection()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 563, in get_connection\n    self.connection = self.__connect()\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/pool.py", line 607, in __connect\n    connection = self.__pool._invoke_creator(self)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/strategies.py", line 97, in connect\n    return dialect.connect(*cargs, **cparams)\n', u'  File "/usr/lib64/python2.7/site-packages/sqlalchemy/engine/default.py", line 385, in connect\n    return self.dbapi.connect(*cargs, **cparams)\n', u'  File "/usr/lib/python2.7/site-packages/pymysql/__init__.py", line 88, in Connect\n    return Connection(*args, **kwargs)\n', u'  File "/usr/lib/python2.7/site-packages/pymysql/connections.py", line 657, in __init__\n    self.connect()\n', u'  File "/usr/lib/python2.7/site-packages/pymysql/connections.py", line 882, in connect\n    raise exc\n', u'DBConnectionError: (pymysql.err.OperationalError) (2003, "Can\'t connect to MySQL server on \'controller\' ([Errno 111] ECONNREFUSED)")\n']."
"nova.servicegroup.drivers.db RemoteError: Remote error: DBConnectionError (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'controller' ([Errno 111] ECONNREFUSED)")"