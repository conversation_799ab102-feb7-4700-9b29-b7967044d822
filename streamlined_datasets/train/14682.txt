"nova.compute.manager [instance: UUID] BuildAbortException: Build of instance UUID aborted: <PERSON><PERSON>or's disk is too small for requested image. Flavor disk is 1073741824 bytes, image is 8589934592 bytes."
nova.compute.manager [instance: UUID]
nova.compute.manager [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Deallocating network for instance _deallocate_network /usr/lib/python2.7/site-packages/nova/compute/manager.py:1824
nova.network.neutronv2.api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] deallocate_for_instance() deallocate_for_instance /usr/lib/python2.7/site-packages/nova/network/neutronv2/api.py:801
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X GET http://controller:9696/v2.0/ports.json?device_id=UUID -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}0a8c9b14ab7205c199e56b7a18f91d1841e656ff" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 851 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 20:34:11 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] REQ: curl -g -i -X DELETE http://controller:9696/v2.0/ports/UUID.json -H "User-Agent: python-neutronclient" -H "Accept: application/json" -H "X-Auth-Token: {SHA1}0a8c9b14ab7205c199e56b7a18f91d1841e656ff" _http_log_request /usr/lib/python2.7/site-packages/keystoneauth1/session.py:248"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [204] Content-Length: 0 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 20:34:11 GMT Connection: keep-alive _http_log_response /usr/lib/python2.7/site-packages/keystoneauth1/session.py:277"
nova.network.base_api [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Updating instance_info_cache with network_info: [] update_instance_cache_with_nw_info /usr/lib/python2.7/site-packages/nova/network/base_api.py:43
nova.compute.manager [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Took 0.25 seconds to deallocate network for instance.
"nova.compute.manager [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] terminating bdm BlockDeviceMapping(boot_index=0,connection_info=None,created_at=2022-01-17T20:34:09Z,delete_on_termination=True,deleted=False,deleted_at=None,destination_type='local',device_name='/dev/vda',device_type='disk',disk_bus=None,guest_format=None,id=110,image_id='UUID',instance=<?>,instance_uuid=UUID,no_device=False,snapshot_id=None,source_type='image',updated_at=2022-01-17T20:34:10Z,volume_id=None,volume_size=None) _cleanup_volumes /usr/lib/python2.7/site-packages/nova/compute/manager.py:2398"
"oslo_concurrency.lockutils [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Lock "UUID" released by "nova.compute.manager._locked_do_build_and_run_instance" :: held 1.817s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"