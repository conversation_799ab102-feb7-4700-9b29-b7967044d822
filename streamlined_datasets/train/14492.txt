nova.compute.manager [REQ_ID - - - - -] Rebuilding the list of instances to heal _heal_instance_info_cache /usr/lib/python2.7/site-packages/nova/compute/manager.py:5745
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 15:18:33 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 15:18:33 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 426 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 15:18:33 GMT Connection: keep-alive"
"nova.virt.libvirt.driver [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] Start _get_guest_xml network_info=[VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': 'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': 'gateway', 'address': u'IP_ADDR'})})], 'meta': {'injected': False, 'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', 'should_create_bridge': True, 'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tap1dd336c9-f8', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:a1:79:c8', 'active': False, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None})] disk_info={'disk_bus': 'virtio', 'cdrom_bus': 'ide', 'mapping': {'disk': {'bus': 'virtio', 'boot_index': '1', 'type': 'disk', 'dev': u'vda'}, 'root': {'bus': 'virtio', 'boot_index': '1', 'type': 'disk', 'dev': u'vda'}}} image_meta=ImageMeta(checksum='cb46d25d147fb5baefdd675a0fd1015d',container_format='bare',created_at=2021-09-20T09:00:02Z,direct_url=<?>,disk_format='qcow2',id=UUID,min_disk=0,min_ram=0,name='centos7',owner='7fe5d95324c448b695017fc3a63492b4',properties=ImageMetaProps,protected=<?>,size=877985792,status='active',tags=<?>,updated_at=2021-09-20T09:00:05Z,virtual_size=<?>,visibility=<?>) rescue=None block_device_info={'swap': None, 'root_device_name': u'/dev/vda', 'ephemerals': [], 'block_device_mapping': []} _get_guest_xml /usr/lib/python2.7/site-packages/nova/virt/libvirt/driver.py:4727"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 543 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 15:18:33 GMT Connection: keep-alive"
"nova.virt.hardware [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] Getting desirable topologies for flavor Flavor(created_at=None,deleted=False,deleted_at=None,disabled=False,ephemeral_gb=0,extra_specs={},flavorid='2',id=5,is_public=True,memory_mb=2048,name='m1.small',projects=<?>,root_gb=20,rxtx_factor=1.0,swap=0,updated_at=None,vcpu_weight=0,vcpus=1) and image_meta ImageMeta(checksum='cb46d25d147fb5baefdd675a0fd1015d',container_format='bare',created_at=2021-09-20T09:00:02Z,direct_url=<?>,disk_format='qcow2',id=UUID,min_disk=0,min_ram=0,name='centos7',owner='7fe5d95324c448b695017fc3a63492b4',properties=ImageMetaProps,protected=<?>,size=877985792,status='active',tags=<?>,updated_at=2021-09-20T09:00:05Z,virtual_size=<?>,visibility=<?>), allow threads: True _get_desirable_cpu_topologies /usr/lib/python2.7/site-packages/nova/virt/hardware.py:568"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 855 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 15:18:33 GMT Connection: keep-alive"
"nova.virt.libvirt.vif [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] vif_type=bridge instance=Instance(access_ip_v4=None,access_ip_v6=None,architecture=None,auto_disk_config=True,availability_zone='nova',cell_name=None,cleaned=False,config_drive='',created_at=2022-01-17T15:18:32Z,default_ephemeral_device=None,default_swap_device=None,deleted=False,deleted_at=None,disable_terminate=False,display_description='f',display_name='f',ec2_ids=EC2Ids,ephemeral_gb=0,ephemeral_key_uuid=None,fault=<?>,flavor=Flavor(5),host='h432-l13',hostname='f',id=105,image_ref='UUID',info_cache=InstanceInfoCache,instance_type_id=5,kernel_id='',key_data=None,key_name=None,launch_index=0,launched_at=None,launched_on='h432-l13',locked=False,locked_by=None,memory_mb=2048,metadata={},migration_context=<?>,new_flavor=None,node='h432-l13',numa_topology=None,old_flavor=None,os_type=None,pci_devices=PciDeviceList,pci_requests=InstancePCIRequests,power_state=0,progress=0,project_id='7fe5d95324c448b695017fc3a63492b4',ramdisk_id='',reservation_id='r-bgpesrka',root_device_name='/dev/vda',root_gb=20,security_groups=SecurityGroupList,services=<?>,shutdown_terminate=False,system_metadata={image_base_image_ref='UUID',image_container_format='bare',image_disk_format='qcow2',image_min_disk='20',image_min_ram='0',network_allocated='True'},tags=<?>,task_state='spawning',terminated_at=None,updated_at=2022-01-17T15:18:32Z,user_data=None,user_id='43b996f08a374a359872ac4444258295',uuid=UUID,vcpu_model=VirtCPUModel,vcpus=1,vm_mode=None,vm_state='building') vif=VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': 'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': 'gateway', 'address': u'IP_ADDR'})})], 'meta': {'injected': False, 'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', 'should_create_bridge': True, 'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tap1dd336c9-f8', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:a1:79:c8', 'active': False, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None}) virt_typekvm get_config /usr/lib/python2.7/site-packages/nova/virt/libvirt/vif.py:437"
"nova.virt.libvirt.vif [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] vif_type=bridge instance=Instance(access_ip_v4=None,access_ip_v6=None,architecture=None,auto_disk_config=True,availability_zone='nova',cell_name=None,cleaned=False,config_drive='',created_at=2022-01-17T15:18:32Z,default_ephemeral_device=None,default_swap_device=None,deleted=False,deleted_at=None,disable_terminate=False,display_description='f',display_name='f',ec2_ids=EC2Ids,ephemeral_gb=0,ephemeral_key_uuid=None,fault=<?>,flavor=Flavor(5),host='h432-l13',hostname='f',id=105,image_ref='UUID',info_cache=InstanceInfoCache,instance_type_id=5,kernel_id='',key_data=None,key_name=None,launch_index=0,launched_at=None,launched_on='h432-l13',locked=False,locked_by=None,memory_mb=2048,metadata={},migration_context=<?>,new_flavor=None,node='h432-l13',numa_topology=None,old_flavor=None,os_type=None,pci_devices=PciDeviceList,pci_requests=InstancePCIRequests,power_state=0,progress=0,project_id='7fe5d95324c448b695017fc3a63492b4',ramdisk_id='',reservation_id='r-bgpesrka',root_device_name='/dev/vda',root_gb=20,security_groups=SecurityGroupList,services=<?>,shutdown_terminate=False,system_metadata={image_base_image_ref='UUID',image_container_format='bare',image_disk_format='qcow2',image_min_disk='20',image_min_ram='0',network_allocated='True'},tags=<?>,task_state='spawning',terminated_at=None,updated_at=2022-01-17T15:18:32Z,user_data=None,user_id='43b996f08a374a359872ac4444258295',uuid=UUID,vcpu_model=VirtCPUModel,vcpus=1,vm_mode=None,vm_state='building') vif=VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': 'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': 'gateway', 'address': u'IP_ADDR'})})], 'meta': {'injected': False, 'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', 'should_create_bridge': True, 'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tap1dd336c9-f8', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:a1:79:c8', 'active': False, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None}) plug /usr/lib/python2.7/site-packages/nova/virt/libvirt/vif.py:744"