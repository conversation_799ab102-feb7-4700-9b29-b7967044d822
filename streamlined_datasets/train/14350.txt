oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._poll_rescued_instances run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/connection.py", line 554, in _x_close_ok"
"oslo.messaging._drivers.impl_rabbit     self._send_method((10, 51))"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/abstract_channel.py", line 56, in _send_method"
"oslo.messaging._drivers.impl_rabbit     self.channel_id, method_sig, args, content,"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/method_framing.py", line 221, in write_method"
"oslo.messaging._drivers.impl_rabbit     write_frame(1, channel, payload)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/transport.py", line 182, in write_frame"
oslo.messaging._drivers.impl_rabbit
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 377, in sendall"
"oslo.messaging._drivers.impl_rabbit     tail = self.send(data, flags)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 359, in send"
"oslo.messaging._drivers.impl_rabbit     total_sent += fd.send(data[total_sent:], flags)"
oslo.messaging._drivers.impl_rabbit error: [Errno 104] Connection reset by peer
oslo.messaging._drivers.impl_rabbit     self._x_close_ok()
"oslo.messaging._drivers.impl_rabbit     frame_type, channel, size, payload, 0xce,"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/connection.py", line 526, in _close"
"oslo.messaging._drivers.impl_rabbit     return self.transport.drain_events(self.connection, **kwargs)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/connection.py", line 323, in drain_events"
oslo.messaging._drivers.impl_rabbit     return connection.drain_events(**kwargs)