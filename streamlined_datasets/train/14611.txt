"oslo_concurrency.lockutils [REQ_ID 7a93481059014a3e86d25a65df66935d 24edcca284a248049028c1352851f520 - - -] Lock "UUID-events" acquired by "nova.compute.manager._pop_event" :: waited 0.000s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:270"
[instance: UUID] Instance spawned successfully.
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
[instance: UUID] Instance destroyed successfully.
"nova.virt.libvirt.vif [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] vif_type=bridge instance=Instance(access_ip_v4=None,access_ip_v6=None,architecture=None,auto_disk_config=True,availability_zone='nova',cell_name=None,cleaned=False,config_drive='',created_at=2022-01-17T18:37:29Z,default_ephemeral_device=None,default_swap_device=None,deleted=False,deleted_at=None,disable_terminate=False,display_description='k',display_name='k',ec2_ids=<?>,ephemeral_gb=0,ephemeral_key_uuid=None,fault=<?>,flavor=Flavor(5),host='h432-l13',hostname='k',id=109,image_ref='UUID',info_cache=InstanceInfoCache,instance_type_id=5,kernel_id='',key_data=None,key_name=None,launch_index=0,launched_at=2022-01-17T18:31:55Z,launched_on='h432-l13',locked=False,locked_by=None,memory_mb=2048,metadata={},migration_context=<?>,new_flavor=None,node='h432-l13',numa_topology=None,old_flavor=None,os_type=None,pci_devices=PciDeviceList,pci_requests=<?>,power_state=1,progress=0,project_id='7fe5d95324c448b695017fc3a63492b4',ramdisk_id='',reservation_id='r-65o6tkxu',root_device_name='/dev/vda',root_gb=20,security_groups=SecurityGroupList,services=<?>,shutdown_terminate=False,system_metadata={image_base_image_ref='UUID',image_container_format='bare',image_disk_format='qcow2',image_min_disk='20',image_min_ram='0'},tags=<?>,task_state='deleting',terminated_at=None,updated_at=2022-01-17T18:38:46Z,user_data=None,user_id='43b996f08a374a359872ac4444258295',uuid=UUID,vcpu_model=<?>,vcpus=1,vm_mode=None,vm_state='active') vif=VIF({'profile': {}, 'ovs_interfaceid': None, 'preserve_on_delete': False, 'network': Network({'bridge': u'brqcd413b9f-77', 'subnets': [Subnet({'ips': [FixedIP({'meta': {}, 'version': 4, 'type': u'fixed', 'floating_ips': [], 'address': u'IP_ADDR'})], 'version': 4, 'meta': {u'dhcp_server': u'IP_ADDR'}, 'dns': [], 'routes': [], 'cidr': u'IP_ADDR/24', 'gateway': IP({'meta': {}, 'version': 4, 'type': u'gateway', 'address': u'IP_ADDR'})})], 'meta': {u'injected': False, u'tenant_id': u'7fe5d95324c448b695017fc3a63492b4', u'should_create_bridge': True, u'mtu': 1450}, 'id': u'UUID', 'label': u'int-net'}), 'devname': u'tapdf150a3e-a9', 'vnic_type': u'normal', 'qbh_params': None, 'meta': {}, 'details': {u'port_filter': True}, 'address': u'fa:16:3e:d2:53:18', 'active': False, 'type': u'bridge', 'id': u'UUID', 'qbg_params': None}) unplug /usr/lib/python2.7/site-packages/nova/virt/libvirt/vif.py:961"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 853 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 18:38:51 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] RESP: [204] Content-Length: 0 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 18:38:51 GMT Connection: keep-alive _http_log_response /usr/lib/python2.7/site-packages/keystoneauth1/session.py:277"
"nova.compute.manager [REQ_ID 43b996f08a374a359872ac4444258295 7fe5d95324c448b695017fc3a63492b4 - - -] [instance: UUID] terminating bdm BlockDeviceMapping(boot_index=0,connection_info=None,created_at=2022-01-17T18:37:29Z,delete_on_termination=True,deleted=False,deleted_at=None,destination_type='local',device_name='/dev/vda',device_type='disk',disk_bus=None,guest_format=None,id=109,image_id='UUID',instance=<?>,instance_uuid=UUID,no_device=False,snapshot_id=None,source_type='image',updated_at=2022-01-17T18:37:30Z,volume_id=None,volume_size=None) _cleanup_volumes /usr/lib/python2.7/site-packages/nova/compute/manager.py:2398"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 18:39:33 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 18:39:33 GMT Connection: keep-alive"