oslo_service.periodic_task [REQ_ID - - - - -] Running periodic task ComputeManager._heal_instance_info_cache run_periodic_tasks /usr/lib/python2.7/site-packages/oslo_service/periodic_task.py:215
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 16:18:30 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 16:18:30 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 19 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 16:18:30 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 543 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 16:18:30 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 855 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 16:18:30 GMT Connection: keep-alive"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/connection.py", line 554, in _x_close_ok"
"oslo.messaging._drivers.impl_rabbit     self._send_method((10, 51))"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/abstract_channel.py", line 56, in _send_method"
"oslo.messaging._drivers.impl_rabbit     self.channel_id, method_sig, args, content,"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/method_framing.py", line 221, in write_method"
"oslo.messaging._drivers.impl_rabbit     write_frame(1, channel, payload)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/amqp/transport.py", line 182, in write_frame"
oslo.messaging._drivers.impl_rabbit
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 377, in sendall"
"oslo.messaging._drivers.impl_rabbit     tail = self.send(data, flags)"
"oslo.messaging._drivers.impl_rabbit   File "/usr/lib/python2.7/site-packages/eventlet/greenio/base.py", line 359, in send"
"oslo.messaging._drivers.impl_rabbit     total_sent += fd.send(data[total_sent:], flags)"
oslo.messaging._drivers.impl_rabbit error: [Errno 104] Connection reset by peer
oslo.messaging._drivers.impl_rabbit     self._x_close_ok()