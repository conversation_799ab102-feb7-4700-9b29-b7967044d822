"oslo_concurrency.lockutils [REQ_ID - - - - -] Lock "compute_resources" released by "nova.compute.resource_tracker._update_available_resource" :: held 0.179s inner /usr/lib/python2.7/site-packages/oslo_concurrency/lockutils.py:282"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:57:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:57:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 426 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:57:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 543 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:57:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 855 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:57:32 GMT Connection: keep-alive"
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] Updated the network info_cache for instance _heal_instance_info_cache /usr/lib/python2.7/site-packages/nova/compute/manager.py:5803
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:58:32 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 06:58:32 GMT Connection: keep-alive"