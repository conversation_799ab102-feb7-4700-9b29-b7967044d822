"oslo_concurrency.processutils [REQ_ID - - - - -] CMD "/usr/bin/python2 -m oslo_concurrency.prlimit --as=1073741824 --cpu=2 -- env LC_ALL=C LANG=C qemu-img info /var/lib/nova/instances/UUID/disk" returned: 0 in 0.105s execute /usr/lib/python2.7/site-packages/oslo_concurrency/processutils.py:374"
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:52:44 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 639 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:52:44 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 19 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:52:44 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 543 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:52:44 GMT Connection: keep-alive"
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 855 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:52:44 GMT Connection: keep-alive"
nova.compute.manager [REQ_ID - - - - -] [instance: UUID] Updated the network info_cache for instance _heal_instance_info_cache /usr/lib/python2.7/site-packages/nova/compute/manager.py:5803
nova.compute.resource_tracker [REQ_ID - - - - -] Compute_service record updated for h432-l13:h432-l13
"keystoneauth.session [REQ_ID - - - - -] RESP: [200] Content-Type: application/json; charset=UTF-8 Content-Length: 852 X-Openstack-Request-Id: REQ_ID Date: Mon, 17 Jan 2022 19:53:46 GMT Connection: keep-alive"