#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强分类器
针对标签2和4的性能提升，目标突破0.97
"""

import os
import csv
import re
import numpy as np
from collections import Counter
from concurrent.futures import ThreadPoolExecutor, as_completed
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, f1_score
from sklearn.utils.class_weight import compute_class_weight
import warnings
warnings.filterwarnings('ignore')

class EnhancedClassifier:
    def __init__(self, data_dir='datasets', n_jobs=8):
        self.data_dir = data_dir
        self.train_dir = os.path.join(data_dir, 'train')
        self.test_dir = os.path.join(data_dir, 'test')
        self.label_file = os.path.join(data_dir, 'label.csv')
        self.n_jobs = n_jobs
        
        self.vectorizer = None
        self.model = None
        
        print(f"增强分类器初始化完成 (使用{n_jobs}个线程)")
        print("专门优化标签2和4的性能")
    
    def load_labels(self):
        """加载标签数据"""
        labels = {}
        try:
            with open(self.label_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    labels[int(row['file_id'])] = int(row['label'])
            print(f"标签加载完成，共{len(labels)}条记录")
        except Exception as e:
            print(f"加载标签文件时出错: {e}")
        return labels
    
    def enhanced_preprocess(self, content):
        """增强预处理"""
        if not content:
            return ""
        
        # 保守的预处理，保留重要信息
        content = re.sub(r'req-[a-f0-9\-]+', ' REQ_TOKEN ', content)
        content = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', ' TIMESTAMP ', content)
        content = re.sub(r'\d+\.\d+\.\d+\.\d+:\d+', ' IP_PORT ', content)
        content = re.sub(r'\d+\.\d+\.\d+\.\d+', ' IP_ADDR ', content)
        content = re.sub(r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ' UUID_TOKEN ', content)
        
        # 保留HTTP状态码的具体信息
        content = re.sub(r'\[2\d{2}\]', ' HTTP_SUCCESS ', content)
        content = re.sub(r'\[4\d{2}\]', ' HTTP_CLIENT_ERROR ', content)
        content = re.sub(r'\[5\d{2}\]', ' HTTP_SERVER_ERROR ', content)
        
        # 保留文件路径信息但标准化
        content = re.sub(r'/usr/lib/python[\d\.]+/site-packages/', ' PYTHON_LIB ', content)
        content = re.sub(r'\.py:\d+', '.py:LINE', content)
        
        return content
    
    def extract_enhanced_features(self, content):
        """提取增强特征，专门优化标签2和4"""
        features = []
        
        if not content:
            return ""
        
        lines = content.split('\n')
        
        # 1. 超精细的首行特征
        if lines:
            first_line = lines[0].strip()
            
            # 针对标签2的特殊首行模式
            if first_line.startswith('nova.compute'):
                features.append("FIRST_nova_compute")
            elif first_line.startswith('nova.network'):
                features.append("FIRST_nova_network")
            elif first_line.startswith('nova.db'):
                features.append("FIRST_nova_db")
            elif first_line.startswith('nova'):
                features.append("FIRST_nova_other")
            
            # 针对标签4的特殊首行模式
            elif first_line.startswith('oslo_service.periodic'):
                features.append("FIRST_oslo_service_periodic")
            elif first_line.startswith('oslo_service.service'):
                features.append("FIRST_oslo_service_service")
            elif first_line.startswith('oslo_service'):
                features.append("FIRST_oslo_service_other")
            
            # 其他重要首行
            elif first_line.startswith('oslo.messaging'):
                features.append("FIRST_oslo_messaging")
            elif first_line.startswith('oslo.'):
                features.append("FIRST_oslo_dot")
            elif first_line.startswith('Received'):
                features.append("FIRST_received")
            elif first_line.startswith('oslo_concurrency'):
                features.append("FIRST_oslo_concurrency")
            elif first_line.startswith('keystoneauth'):
                features.append("FIRST_keystoneauth")
            elif first_line.startswith('"oslo'):
                features.append("FIRST_quoted_oslo")
            elif first_line.startswith('"nova'):
                features.append("FIRST_quoted_nova")
            elif first_line.startswith('Function'):
                features.append("FIRST_function")
        
        # 2. 超精细的错误率特征
        content_upper = content.upper()
        error_count = content_upper.count('ERROR')
        info_count = content_upper.count('INFO')
        warning_count = content_upper.count('WARNING') + content_upper.count('WARN')
        total_logs = error_count + info_count + warning_count
        
        if total_logs > 0:
            error_ratio = error_count / total_logs
            # 更精细的错误率分档
            if error_ratio > 0.95:
                features.append("ERROR_RATIO_extreme")
            elif error_ratio > 0.8:
                features.append("ERROR_RATIO_very_high")
            elif error_ratio > 0.6:
                features.append("ERROR_RATIO_high")
            elif error_ratio > 0.4:
                features.append("ERROR_RATIO_medium_high")
            elif error_ratio > 0.25:
                features.append("ERROR_RATIO_medium")
            elif error_ratio > 0.15:
                features.append("ERROR_RATIO_medium_low")
            elif error_ratio > 0.08:
                features.append("ERROR_RATIO_low")
            elif error_ratio > 0.02:
                features.append("ERROR_RATIO_very_low")
            elif error_ratio > 0:
                features.append("ERROR_RATIO_minimal")
            else:
                features.append("ERROR_RATIO_zero")
        
        features.append(f"ERROR_COUNT_{min(error_count, 20)}")
        features.append(f"INFO_COUNT_{min(info_count//8, 25)}")
        
        # 3. 标签2专用特征（混合操作日志）
        content_lower = content.lower()
        
        # SQL相关特征
        sql_patterns = {
            'select': content_lower.count('select'),
            'insert': content_lower.count('insert'),
            'update': content_lower.count('update'),
            'delete': content_lower.count('delete'),
            'query': content_lower.count('query'),
            'sql': content_lower.count('sql'),
            'mysql': content_lower.count('mysql'),
            'database': content_lower.count('database')
        }
        
        sql_total = sum(sql_patterns.values())
        if sql_total > 0:
            features.append(f"SQL_total_score_{min(sql_total, 10)}")
            
            # 具体SQL操作
            for op, count in sql_patterns.items():
                if count > 0:
                    features.append(f"SQL_{op}_{min(count, 5)}")
        
        # 服务不可用特征（标签2特有）
        if '503' in content or 'service unavailable' in content_lower:
            features.append("HAS_503_service_unavailable")
            unavailable_count = content_lower.count('unavailable')
            features.append(f"UNAVAILABLE_count_{min(unavailable_count, 5)}")
        
        # 4. 标签4专用特征（周期任务日志）
        periodic_patterns = {
            'periodic': content_lower.count('periodic'),
            'task': content_lower.count('task'),
            'schedule': content_lower.count('schedule'),
            'manager': content_lower.count('manager'),
            'running': content_lower.count('running'),
            'execute': content_lower.count('execute'),
            'worker': content_lower.count('worker')
        }
        
        periodic_total = sum(periodic_patterns.values())
        if periodic_total > 0:
            features.append(f"PERIODIC_total_score_{min(periodic_total, 8)}")
            
            # 具体周期任务特征
            for pattern, count in periodic_patterns.items():
                if count > 0:
                    features.append(f"PERIODIC_{pattern}_{min(count, 3)}")
        
        # 特殊周期任务模式
        if 'periodic_task' in content_lower:
            features.append("HAS_periodic_task_pattern")
        if 'running periodic' in content_lower:
            features.append("HAS_running_periodic")
        if 'task manager' in content_lower:
            features.append("HAS_task_manager")
        
        # 5. 其他标签的关键特征
        # 消息队列特征（标签1）
        messaging_score = sum(content_lower.count(kw) for kw in ['messaging', 'rabbit', 'amqp', 'queue'])
        if messaging_score > 0:
            features.append(f"MESSAGING_score_{min(messaging_score, 10)}")
        
        # 认证特征（标签3）
        auth_score = sum(content_lower.count(kw) for kw in ['keystone', 'auth', 'token', 'credential'])
        if auth_score > 0:
            features.append(f"AUTH_score_{min(auth_score, 8)}")
        
        # 网络错误特征（标签5）
        network_score = sum(content_lower.count(kw) for kw in ['timeout', 'connection', 'refused', 'failed'])
        if network_score > 0:
            features.append(f"NETWORK_ERROR_score_{min(network_score, 8)}")
        
        # 6. 错误模式特征
        if 'traceback' in content_lower or 'exception' in content_lower:
            features.append("HAS_stacktrace")
            
            # 异常类型
            if 'sqlalchemy' in content_lower:
                features.append("HAS_sqlalchemy_exception")
            if 'timeout' in content_lower and 'exception' in content_lower:
                features.append("HAS_timeout_exception")
        
        # HTTP错误码
        http_errors = {
            '401': 'HAS_401_unauthorized',
            '403': 'HAS_403_forbidden', 
            '404': 'HAS_404_notfound',
            '500': 'HAS_500_internal',
            '503': 'HAS_503_unavailable'
        }
        
        for code, feature in http_errors.items():
            if code in content:
                features.append(feature)
        
        # 7. 文本结构特征
        features.append(f"line_count_{min(len(lines)//12, 25)}")
        features.append(f"char_count_{min(len(content)//600, 80)}")
        
        # 平均行长度
        avg_line_length = len(content) / max(len(lines), 1)
        features.append(f"avg_line_length_{min(int(avg_line_length//40), 25)}")
        
        # 8. 组件复杂度
        components = []
        for line in lines[:15]:  # 检查更多行
            line = line.strip()
            if line and not line.startswith('"'):
                match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_\.]*)', line)
                if match:
                    component = match.group(1).split('.')[0]
                    components.append(component)
        
        unique_components = len(set(components))
        features.append(f"component_diversity_{min(unique_components, 10)}")
        
        # 主导组件
        if components:
            component_counter = Counter(components)
            dominant_component = component_counter.most_common(1)[0][0]
            features.append(f"dominant_component_{dominant_component}")
        
        return " ".join(features)
    
    def process_file_enhanced(self, args):
        """增强的文件处理"""
        filename, labels_dict = args
        file_id = int(filename.replace('.txt', ''))
        
        if file_id not in labels_dict:
            return None
        
        file_path = os.path.join(self.train_dir, filename)
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()
            
            # 增强预处理
            processed_content = self.enhanced_preprocess(content)
            
            # 提取增强特征
            enhanced_features = self.extract_enhanced_features(content)
            
            # 组合文本和特征
            combined_text = processed_content + " " + enhanced_features
            
            return {
                'text': combined_text,
                'label': labels_dict[file_id],
                'file_id': file_id
            }
        except:
            return None
    
    def load_data_enhanced(self):
        """增强的数据加载"""
        print("开始增强数据加载...")
        
        labels_dict = self.load_labels()
        train_files = [f for f in os.listdir(self.train_dir) if f.endswith('.txt')]
        
        print(f"使用{self.n_jobs}个线程处理{len(train_files)}个文件...")
        
        texts, labels = [], []
        
        with ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
            future_to_file = {
                executor.submit(self.process_file_enhanced, (filename, labels_dict)): filename 
                for filename in train_files
            }
            
            processed = 0
            for future in as_completed(future_to_file):
                result = future.result()
                if result:
                    texts.append(result['text'])
                    labels.append(result['label'])
                
                processed += 1
                if processed % 3000 == 0:
                    print(f"已处理 {processed}/{len(train_files)} 个文件")
        
        print(f"数据加载完成，共{len(texts)}条记录")
        
        # 标签分布
        label_counts = Counter(labels)
        print("标签分布:")
        for label in sorted(label_counts.keys()):
            count = label_counts[label]
            percentage = count / len(labels) * 100
            print(f"  标签 {label}: {count} 条 ({percentage:.2f}%)")
        
        return texts, labels

    def create_enhanced_model(self, y_train):
        """创建增强模型"""
        print("创建增强集成模型...")

        # 计算类别权重
        unique_labels = np.unique(y_train)
        class_weights = compute_class_weight('balanced', classes=unique_labels, y=y_train)
        class_weight_dict = dict(zip(unique_labels, class_weights))

        # 对标签2和4给予额外权重提升
        for label in [2, 4]:
            if label in class_weight_dict:
                class_weight_dict[label] *= 1.3  # 增加30%权重

        print("增强后类别权重:")
        for label, weight in class_weight_dict.items():
            print(f"  标签 {label}: {weight:.3f}")

        # 使用三模型集成，参数精调
        models = [
            ('lr', LogisticRegression(
                class_weight=class_weight_dict,
                max_iter=1200,
                random_state=42,
                C=0.25,  # 更强的正则化
                solver='liblinear'
            )),
            ('rf', RandomForestClassifier(
                class_weight=class_weight_dict,
                n_estimators=300,  # 增加树的数量
                max_depth=18,      # 增加深度
                min_samples_split=3,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                n_jobs=self.n_jobs
            )),
            ('gb', GradientBoostingClassifier(
                n_estimators=200,
                learning_rate=0.08,  # 稍微降低学习率
                max_depth=10,
                min_samples_split=4,
                min_samples_leaf=2,
                subsample=0.9,
                random_state=42
            ))
        ]

        ensemble = VotingClassifier(
            estimators=models,
            voting='soft',
            n_jobs=self.n_jobs
        )

        return ensemble

    def train_enhanced(self, texts, labels, test_size=0.3):
        """增强训练"""
        print("\n开始增强训练...")

        # 分层划分
        X_train, X_val, y_train, y_val = train_test_split(
            texts, labels, test_size=test_size, random_state=42, stratify=labels
        )

        print(f"训练集: {len(X_train)}, 验证集: {len(X_val)}")

        # 增强向量化
        print("进行增强向量化...")
        self.vectorizer = TfidfVectorizer(
            max_features=28000,  # 进一步增加特征数
            ngram_range=(1, 3),
            min_df=2,
            max_df=0.82,  # 进一步降低max_df
            sublinear_tf=True,
            analyzer='word',
            token_pattern=r'\b\w+\b'
        )

        X_train_vec = self.vectorizer.fit_transform(X_train)
        X_val_vec = self.vectorizer.transform(X_val)

        print(f"特征维度: {X_train_vec.shape[1]}")

        # 创建并训练模型
        self.model = self.create_enhanced_model(y_train)

        print("训练增强模型...")
        self.model.fit(X_train_vec, y_train)

        # 验证
        print("验证模型...")
        y_val_pred = self.model.predict(X_val_vec)

        macro_f1 = f1_score(y_val, y_val_pred, average='macro')
        print(f"\n验证集 Macro F1-score: {macro_f1:.4f}")

        print("\n详细分类报告:")
        print(classification_report(y_val, y_val_pred, digits=4))

        return macro_f1

    def predict_test_enhanced(self):
        """增强的测试集预测"""
        print("\n开始增强测试集预测...")

        test_files = sorted([f for f in os.listdir(self.test_dir) if f.endswith('.txt')],
                           key=lambda x: int(x.replace('.txt', '')))

        def process_test_file(filename):
            file_id = int(filename.replace('.txt', ''))
            file_path = os.path.join(self.test_dir, filename)

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()

                processed_content = self.enhanced_preprocess(content)
                enhanced_features = self.extract_enhanced_features(content)
                combined_text = processed_content + " " + enhanced_features

                return file_id, combined_text
            except:
                return file_id, ""

        # 并行处理测试文件
        test_data = []
        with ThreadPoolExecutor(max_workers=self.n_jobs) as executor:
            futures = [executor.submit(process_test_file, f) for f in test_files]

            for i, future in enumerate(as_completed(futures)):
                test_data.append(future.result())
                if (i + 1) % 1000 == 0:
                    print(f"已处理 {i + 1}/{len(test_files)} 个测试文件")

        # 按ID排序
        test_data.sort(key=lambda x: x[0])
        test_ids, test_texts = zip(*test_data)

        # 向量化和预测
        X_test_vec = self.vectorizer.transform(test_texts)
        predictions = self.model.predict(X_test_vec)

        # 保存结果
        output_file = 'enhanced_predictions.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['id', 'label'])
            for test_id, pred in zip(test_ids, predictions):
                writer.writerow([test_id, pred])

        print(f"测试集预测完成，结果保存到 {output_file}")

        # 预测分布
        pred_counts = Counter(predictions)
        print("测试集预测分布:")
        for label in sorted(pred_counts.keys()):
            count = pred_counts[label]
            percentage = count / len(predictions) * 100
            print(f"  标签 {label}: {count} 条 ({percentage:.2f}%)")

        return predictions

def main():
    """主函数"""
    print("增强OpenStack日志分类任务")
    print("专门优化标签2和4，目标突破0.97")
    print("=" * 50)

    # 创建增强分类器
    classifier = EnhancedClassifier(n_jobs=8)

    # 增强数据加载
    texts, labels = classifier.load_data_enhanced()

    if len(texts) == 0:
        print("数据加载失败")
        return

    # 增强训练
    macro_f1 = classifier.train_enhanced(texts, labels)

    # 增强预测
    predictions = classifier.predict_test_enhanced()

    print("\n" + "=" * 50)
    print("增强任务完成!")
    print(f"验证集 Macro F1-score: {macro_f1:.4f}")

    if macro_f1 > 0.97:
        print("🎉 成功突破0.97！")
    elif macro_f1 > 0.965:
        print("🚀 非常接近目标！")
    else:
        print("📈 继续优化中...")

if __name__ == "__main__":
    main()
